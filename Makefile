# Image URL to use all building/pushing image targets
IMG ?= controller:latest
# ENVTEST_K8S_VERSION refers to the version of kubebuilder assets to be downloaded by envtest binary.
ENVTEST_K8S_VERSION = 1.31.0

# Get the currently used golang install path (in GOPATH/bin, unless GO<PERSON><PERSON> is set)
ifeq (,$(shell go env GOBIN))
GOBIN=$(shell go env GOPATH)/bin
else
GOBIN=$(shell go env GOBIN)
endif

# CONTAINER_TOOL defines the container tool to be used for building images.
# Be aware that the target commands are only tested with Dock<PERSON> which is
# scaffolded by default. However, you might want to replace it to use other
# tools. (i.e. podman)
CONTAINER_TOOL ?= docker

BUILD_TIME=$(shell date -u '+%Y-%m-%d_%H:%M:%S')
GitCommit=$(shell git rev-parse HEAD)
Version="0.1.0"

# Setting SHELL to bash allows bash commands to be executed by recipes.
# Options are set to exit when a recipe line exits non-zero or a piped command fails.
SHELL = /usr/bin/env bash -o pipefail
.SHELLFLAGS = -ec

.PHONY: all
all: build

##@ General

# The help target prints out all targets with their descriptions organized
# beneath their categories. The categories are represented by '##@' and the
# target descriptions by '##'. The awk command is responsible for reading the
# entire set of makefiles included in this invocation, looking for lines of the
# file as xyz: ## something, and then pretty-format the target and help. Then,
# if there's a line with ##@ something, that gets pretty-printed as a category.
# More info on the usage of ANSI control characters for terminal formatting:
# https://en.wikipedia.org/wiki/ANSI_escape_code#SGR_parameters
# More info on the awk command:
# http://linuxcommand.org/lc3_adv_awk.php

.PHONY: help
help: ## Display this help.
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z_0-9-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

##@ Development

.PHONY: manifests
manifests: controller-gen ## Generate WebhookConfiguration, ClusterRole and CustomResourceDefinition objects.
	$(CONTROLLER_GEN) rbac:roleName=manager-role crd webhook paths="./..." output:crd:artifacts:config=config/crd/bases

.PHONY: generate
generate: controller-gen ## Generate code containing DeepCopy, DeepCopyInto, and DeepCopyObject method implementations.
	$(CONTROLLER_GEN) object:headerFile="hack/boilerplate.go.txt" paths="./..."

.PHONY: fmt
fmt: ## Run go fmt against code.
	go fmt ./...

.PHONY: vet
vet: ## Run go vet against code.
	go vet ./...

.PHONY: test
test: manifests generate fmt vet envtest ## Run tests.
	KUBEBUILDER_ASSETS="$(shell $(ENVTEST) use $(ENVTEST_K8S_VERSION) --bin-dir $(LOCALBIN) -p path)" go test $$(go list ./... | grep -v /e2e) -coverprofile cover.out

# TODO(user): To use a different vendor for e2e tests, modify the setup under 'tests/e2e'.
# The default setup assumes Kind is pre-installed and builds/loads the Manager Docker image locally.
# Prometheus and CertManager are installed by default; skip with:
# - PROMETHEUS_INSTALL_SKIP=true
# - CERT_MANAGER_INSTALL_SKIP=true
.PHONY: test-e2e
test-e2e: manifests generate fmt vet ## Run the e2e tests. Expected an isolated environment using Kind.
	@command -v kind >/dev/null 2>&1 || { \
		echo "Kind is not installed. Please install Kind manually."; \
		exit 1; \
	}
	@kind get clusters | grep -q 'kind' || { \
		echo "No Kind cluster is running. Please start a Kind cluster before running the e2e tests."; \
		exit 1; \
	}
	go test ./test/e2e/ -v -ginkgo.v

.PHONY: lint
lint: golangci-lint ## Run golangci-lint linter
	$(GOLANGCI_LINT) run

.PHONY: lint-fix
lint-fix: golangci-lint ## Run golangci-lint linter and perform fixes
	$(GOLANGCI_LINT) run --fix

##@ Build

.PHONY: build
build: manifests generate fmt vet ## Build manager binary.
	CGO_ENABLED=0 go build -ldflags "-X kunpeng.huawei.com/tap/pkg/version.Version=$(Version) -X kunpeng.huawei.com/tap/pkg/version.Built=$(BUILD_TIME) -X kunpeng.huawei.com/tap/pkg/version.GitCommit=$(GitCommit)" -o bin/kunpeng-tap cmd/proxy/main.go
	CGO_ENABLED=0 go build -o bin/manager cmd/manager/main.go

.PHONY: run
run: manifests generate fmt vet ## Run a controller from your host.
	go run ./cmd/main.go

.PHONY: install-service
install-service: install-service-docker

.PHONY: install-service-docker
install-service-docker:
	cp ./bin/kunpeng-tap /usr/local/bin/kunpeng-tap
	chmod 755 /usr/local/bin/kunpeng-tap
	cp ./hack/kunpeng-tap.service.docker /etc/systemd/system/kunpeng-tap.service
	chmod 644 /etc/systemd/system/kunpeng-tap.service
	systemctl daemon-reload
	systemctl enable kunpeng-tap.service

.PHONY: install-service-containerd
install-service-containerd:
	cp ./bin/kunpeng-tap /usr/local/bin/kunpeng-tap
	chmod 755 /usr/local/bin/kunpeng-tap
	cp ./hack/kunpeng-tap.service.containerd /etc/systemd/system/kunpeng-tap.service
	chmod 644 /etc/systemd/system/kunpeng-tap.service
	systemctl daemon-reload
	systemctl enable kunpeng-tap.service

.PHONY: start-service
start-service:
	systemctl start kunpeng-tap.service
	systemctl status kunpeng-tap.service

.PHONY: uninstall-service
uninstall-service:
	systemctl stop kunpeng-tap.service
	systemctl disable kunpeng-tap.service
	rm -f /etc/systemd/system/kunpeng-tap.service
	rm -f /usr/local/bin/kunpeng-tap
	systemctl daemon-reload

# If you wish to build the manager image targeting other platforms you can use the --platform flag.
# (i.e. docker build --platform linux/arm64). However, you must enable docker buildKit for it.
# More info: https://docs.docker.com/develop/develop-images/build_enhancements/
.PHONY: docker-build
docker-build: ## Build docker image with the manager.
	$(CONTAINER_TOOL) build -t ${IMG} .

.PHONY: docker-push
docker-push: ## Push docker image with the manager.
	$(CONTAINER_TOOL) push ${IMG}

# PLATFORMS defines the target platforms for the manager image be built to provide support to multiple
# architectures. (i.e. make docker-buildx IMG=myregistry/mypoperator:0.0.1). To use this option you need to:
# - be able to use docker buildx. More info: https://docs.docker.com/build/buildx/
# - have enabled BuildKit. More info: https://docs.docker.com/develop/develop-images/build_enhancements/
# - be able to push the image to your registry (i.e. if you do not set a valid value via IMG=<myregistry/image:<tag>> then the export will fail)
# To adequately provide solutions that are compatible with multiple platforms, you should consider using this option.
PLATFORMS ?= linux/arm64,linux/amd64,linux/s390x,linux/ppc64le
.PHONY: docker-buildx
docker-buildx: ## Build and push docker image for the manager for cross-platform support
	# copy existing Dockerfile and insert --platform=${BUILDPLATFORM} into Dockerfile.cross, and preserve the original Dockerfile
	sed -e '1 s/\(^FROM\)/FROM --platform=\$$\{BUILDPLATFORM\}/; t' -e ' 1,// s//FROM --platform=\$$\{BUILDPLATFORM\}/' Dockerfile > Dockerfile.cross
	- $(CONTAINER_TOOL) buildx create --name topo-affinity-plugin-builder
	$(CONTAINER_TOOL) buildx use topo-affinity-plugin-builder
	- $(CONTAINER_TOOL) buildx build --push --platform=$(PLATFORMS) --tag ${IMG} -f Dockerfile.cross .
	- $(CONTAINER_TOOL) buildx rm topo-affinity-plugin-builder
	rm Dockerfile.cross

.PHONY: build-installer
build-installer: manifests generate kustomize ## Generate a consolidated YAML with CRDs and deployment.
	mkdir -p dist
	cd config/manager && $(KUSTOMIZE) edit set image controller=${IMG}
	$(KUSTOMIZE) build config/default > dist/install.yaml

##@ Deployment

ifndef ignore-not-found
  ignore-not-found = false
endif

.PHONY: install
install: manifests kustomize ## Install CRDs into the K8s cluster specified in ~/.kube/config.
	$(KUSTOMIZE) build config/crd | $(KUBECTL) apply -f -

.PHONY: uninstall
uninstall: manifests kustomize ## Uninstall CRDs from the K8s cluster specified in ~/.kube/config. Call with ignore-not-found=true to ignore resource not found errors during deletion.
	$(KUSTOMIZE) build config/crd | $(KUBECTL) delete --ignore-not-found=$(ignore-not-found) -f -

.PHONY: deploy
deploy: manifests kustomize ## Deploy controller to the K8s cluster specified in ~/.kube/config.
	cd config/manager && $(KUSTOMIZE) edit set image controller=${IMG}
	$(KUSTOMIZE) build config/default | $(KUBECTL) apply -f -

.PHONY: undeploy
undeploy: kustomize ## Undeploy controller from the K8s cluster specified in ~/.kube/config. Call with ignore-not-found=true to ignore resource not found errors during deletion.
	$(KUSTOMIZE) build config/default | $(KUBECTL) delete --ignore-not-found=$(ignore-not-found) -f -

##@ Dependencies

## Location to install dependencies to
LOCALBIN ?= $(shell pwd)/bin
$(LOCALBIN):
	mkdir -p $(LOCALBIN)

## Tool Binaries
KUBECTL ?= kubectl
KUSTOMIZE ?= $(LOCALBIN)/kustomize
CONTROLLER_GEN ?= $(LOCALBIN)/controller-gen
ENVTEST ?= $(LOCALBIN)/setup-envtest
GOLANGCI_LINT = $(LOCALBIN)/golangci-lint

## Tool Versions
KUSTOMIZE_VERSION ?= v5.5.0
CONTROLLER_TOOLS_VERSION ?= v0.16.4
ENVTEST_VERSION ?= release-0.19
GOLANGCI_LINT_VERSION ?= v1.61.0

.PHONY: kustomize
kustomize: $(KUSTOMIZE) ## Download kustomize locally if necessary.
$(KUSTOMIZE): $(LOCALBIN)
	$(call go-install-tool,$(KUSTOMIZE),sigs.k8s.io/kustomize/kustomize/v5,$(KUSTOMIZE_VERSION))

.PHONY: controller-gen
controller-gen: $(CONTROLLER_GEN) ## Download controller-gen locally if necessary.
$(CONTROLLER_GEN): $(LOCALBIN)
	$(call go-install-tool,$(CONTROLLER_GEN),sigs.k8s.io/controller-tools/cmd/controller-gen,$(CONTROLLER_TOOLS_VERSION))

.PHONY: envtest
envtest: $(ENVTEST) ## Download setup-envtest locally if necessary.
$(ENVTEST): $(LOCALBIN)
	$(call go-install-tool,$(ENVTEST),sigs.k8s.io/controller-runtime/tools/setup-envtest,$(ENVTEST_VERSION))

.PHONY: golangci-lint
golangci-lint: $(GOLANGCI_LINT) ## Download golangci-lint locally if necessary.
$(GOLANGCI_LINT): $(LOCALBIN)
	$(call go-install-tool,$(GOLANGCI_LINT),github.com/golangci/golangci-lint/cmd/golangci-lint,$(GOLANGCI_LINT_VERSION))

# go-install-tool will 'go install' any package with custom target and name of binary, if it doesn't exist
# $1 - target path with name of binary
# $2 - package url which can be installed
# $3 - specific version of package
define go-install-tool
@[ -f "$(1)-$(3)" ] || { \
set -e; \
package=$(2)@$(3) ;\
echo "Downloading $${package}" ;\
rm -f $(1) || true ;\
GOBIN=$(LOCALBIN) go install $${package} ;\
mv $(1) $(1)-$(3) ;\
} ;\
ln -sf $(1)-$(3) $(1)
endef

##@ RPM Package

# RPM package variables
RPM_NAME ?= kunpeng-tap
RPM_VERSION ?= $(shell echo $(GIT_VERSION) | sed 's/^v//')
RPM_RELEASE ?= 1
RPM_ARCH ?= aarch64
RUNTIME_TYPE ?= docker

# RPM build directories
RPM_BUILD_DIR ?= $(shell pwd)/rpmbuild
RPM_SOURCES_DIR ?= $(RPM_BUILD_DIR)/SOURCES
RPM_SPECS_DIR ?= $(RPM_BUILD_DIR)/SPECS
RPM_RPMS_DIR ?= $(RPM_BUILD_DIR)/RPMS
RPM_BUILD_ROOT ?= $(RPM_BUILD_DIR)/BUILDROOT

.PHONY: rpm-prepare
rpm-prepare: ## Prepare RPM build environment
	@echo "Preparing RPM build environment..."
	@mkdir -p $(RPM_SOURCES_DIR) $(RPM_SPECS_DIR) $(RPM_RPMS_DIR) $(RPM_BUILD_ROOT)
	@echo "RPM build directories created"

.PHONY: rpm-source
rpm-source: build-kunpeng-tap rpm-prepare ## Create source tarball for RPM
	@echo "Creating source tarball for RPM..."
	@mkdir -p $(RPM_NAME)-$(RPM_VERSION)
	@cp -r bin/ $(RPM_NAME)-$(RPM_VERSION)/
	@mkdir -p $(RPM_NAME)-$(RPM_VERSION)/hack
	@cp hack/kunpeng-tap.service.docker $(RPM_NAME)-$(RPM_VERSION)/hack/
	@cp hack/kunpeng-tap.service.containerd $(RPM_NAME)-$(RPM_VERSION)/hack/
	@cp -r cmd/ $(RPM_NAME)-$(RPM_VERSION)/ 2>/dev/null || echo "cmd directory not found, skipping..."
	@cp -r pkg/ $(RPM_NAME)-$(RPM_VERSION)/ 2>/dev/null || echo "pkg directory not found, skipping..."
	@cp -r apis/ $(RPM_NAME)-$(RPM_VERSION)/ 2>/dev/null || echo "apis directory not found, skipping..."
	@cp go.mod $(RPM_NAME)-$(RPM_VERSION)/ 2>/dev/null || echo "go.mod file not found, skipping..."
	@cp go.sum $(RPM_NAME)-$(RPM_VERSION)/ 2>/dev/null || echo "go.sum file not found, skipping..."
	@cp Makefile $(RPM_NAME)-$(RPM_VERSION)/ 2>/dev/null || echo "Makefile not found, skipping..."
	@cp LICENSE $(RPM_NAME)-$(RPM_VERSION)/ 2>/dev/null || echo "LICENSE file not found, skipping..."
	@cp README.md $(RPM_NAME)-$(RPM_VERSION)/ 2>/dev/null || echo "README.md file not found, skipping..."
	@tar -czf $(RPM_SOURCES_DIR)/$(RPM_NAME)-$(RPM_VERSION).tar.gz $(RPM_NAME)-$(RPM_VERSION)
	@rm -rf $(RPM_NAME)-$(RPM_VERSION)
	@echo "Source tarball created: $(RPM_SOURCES_DIR)/$(RPM_NAME)-$(RPM_VERSION).tar.gz"

.PHONY: rpm-spec
rpm-spec: rpm-prepare ## Copy spec file to RPM build directory
	@echo "Copying spec file..."
	@cp kunpeng-tap.spec $(RPM_SPECS_DIR)/
	@echo "Spec file copied to $(RPM_SPECS_DIR)/"

.PHONY: rpm-build
rpm-build: rpm-source rpm-spec ## Build RPM package
	@echo "Building RPM package..."
	@echo "Runtime type: $(RUNTIME_TYPE)"
	@rpmbuild --define "_topdir $(RPM_BUILD_DIR)" \
		--define "_version $(RPM_VERSION)" \
		--define "_release $(RPM_RELEASE)" \
		--define "runtime_type $(RUNTIME_TYPE)" \
		-bb $(RPM_SPECS_DIR)/kunpeng-tap.spec
	@echo "RPM package built successfully!"
	@echo "Package location: $(RPM_RPMS_DIR)/$(RPM_ARCH)/"
	@ls -la $(RPM_RPMS_DIR)/$(RPM_ARCH)/

.PHONY: rpm-build-docker
rpm-build-docker: ## Build RPM package for Docker runtime
	@$(MAKE) rpm-build RUNTIME_TYPE=docker

.PHONY: rpm-build-containerd
rpm-build-containerd: ## Build RPM package for Containerd runtime
	@$(MAKE) rpm-build RUNTIME_TYPE=containerd

.PHONY: rpm-clean
rpm-clean: ## Clean RPM build artifacts
	@echo "Cleaning RPM build artifacts..."
	@rm -rf $(RPM_BUILD_DIR)
	@echo "RPM build artifacts cleaned"

.PHONY: rpm-install
rpm-install: ## Install the built RPM package
	@echo "Installing RPM package..."
	@if [ ! -f $(RPM_RPMS_DIR)/$(RPM_ARCH)/$(RPM_NAME)-$(RPM_VERSION)-$(RPM_RELEASE).$(RPM_ARCH).rpm ]; then \
		echo "RPM package not found. Please run 'make rpm-build' first."; \
		exit 1; \
	fi
	@sudo rpm -ivh $(RPM_RPMS_DIR)/$(RPM_ARCH)/$(RPM_NAME)-$(RPM_VERSION)-$(RPM_RELEASE).$(RPM_ARCH).rpm
	@echo "RPM package installed successfully!"

.PHONY: rpm-uninstall
rpm-uninstall: ## Uninstall the RPM package
	@echo "Uninstalling RPM package..."
	@sudo rpm -e $(RPM_NAME) || echo "Package not installed or already removed"
	@echo "RPM package uninstalled"

.PHONY: rpm-info
rpm-info: ## Show information about the built RPM package
	@echo "RPM Package Information:"
	@echo "Name: $(RPM_NAME)"
	@echo "Version: $(RPM_VERSION)"
	@echo "Release: $(RPM_RELEASE)"
	@echo "Architecture: $(RPM_ARCH)"
	@echo "Runtime Type: $(RUNTIME_TYPE)"
	@echo "Build Directory: $(RPM_BUILD_DIR)"
	@if [ -f $(RPM_RPMS_DIR)/$(RPM_ARCH)/$(RPM_NAME)-$(RPM_VERSION)-$(RPM_RELEASE).$(RPM_ARCH).rpm ]; then \
		echo "Package File: $(RPM_RPMS_DIR)/$(RPM_ARCH)/$(RPM_NAME)-$(RPM_VERSION)-$(RPM_RELEASE).$(RPM_ARCH).rpm"; \
		echo "Package Size: $$(du -h $(RPM_RPMS_DIR)/$(RPM_ARCH)/$(RPM_NAME)-$(RPM_VERSION)-$(RPM_RELEASE).$(RPM_ARCH).rpm | cut -f1)"; \
	else \
		echo "Package File: Not built yet"; \
	fi

.PHONY: rpm-verify
rpm-verify: ## Verify the built RPM package
	@echo "Verifying RPM package..."
	@if [ -f $(RPM_RPMS_DIR)/$(RPM_ARCH)/$(RPM_NAME)-$(RPM_VERSION)-$(RPM_RELEASE).$(RPM_ARCH).rpm ]; then \
		rpm -qip $(RPM_RPMS_DIR)/$(RPM_ARCH)/$(RPM_NAME)-$(RPM_VERSION)-$(RPM_RELEASE).$(RPM_ARCH).rpm; \
		echo ""; \
		echo "Package contents:"; \
		rpm -qlp $(RPM_RPMS_DIR)/$(RPM_ARCH)/$(RPM_NAME)-$(RPM_VERSION)-$(RPM_RELEASE).$(RPM_ARCH).rpm; \
	else \
		echo "RPM package not found. Please run 'make rpm-build' first."; \
		exit 1; \
	fi

.PHONY: rpm-test-install
rpm-test-install: ## Test install the RPM package (dry run)
	@echo "Testing RPM package installation (dry run)..."
	@if [ -f $(RPM_RPMS_DIR)/$(RPM_ARCH)/$(RPM_NAME)-$(RPM_VERSION)-$(RPM_RELEASE).$(RPM_ARCH).rpm ]; then \
		rpm -ivh --test $(RPM_RPMS_DIR)/$(RPM_ARCH)/$(RPM_NAME)-$(RPM_VERSION)-$(RPM_RELEASE).$(RPM_ARCH).rpm; \
		echo "Test installation completed successfully!"; \
	else \
		echo "RPM package not found. Please run 'make rpm-build' first."; \
		exit 1; \
	fi

.PHONY: rpm-all
rpm-all: rpm-clean rpm-build rpm-verify ## Complete RPM build and verification process
	@echo "Complete RPM build process finished!"
	@echo "Package ready at: $(RPM_RPMS_DIR)/$(RPM_ARCH)/"

.PHONY: rpm-help
rpm-help: ## Show RPM-related make targets
	@echo "Available RPM targets:"
	@echo "  rpm-prepare          - Prepare RPM build environment"
	@echo "  rpm-source           - Create source tarball for RPM"
	@echo "  rpm-spec             - Copy spec file to RPM build directory"
	@echo "  rpm-build            - Build RPM package"
	@echo "  rpm-build-docker     - Build RPM package for Docker runtime"
	@echo "  rpm-build-containerd - Build RPM package for Containerd runtime"
	@echo "  rpm-verify           - Verify the built RPM package"
	@echo "  rpm-test-install     - Test install the RPM package (dry run)"
	@echo "  rpm-install          - Install the built RPM package"
	@echo "  rpm-uninstall        - Uninstall the RPM package"
	@echo "  rpm-clean            - Clean RPM build artifacts"
	@echo "  rpm-info             - Show information about the built RPM package"
	@echo "  rpm-all              - Complete RPM build and verification process"
	@echo ""
	@echo "Variables:"
	@echo "  RUNTIME_TYPE=$(RUNTIME_TYPE)  - Container runtime type (docker/containerd)"
	@echo "  RPM_VERSION=$(RPM_VERSION)    - RPM package version"
	@echo "  RPM_RELEASE=$(RPM_RELEASE)    - RPM package release"
